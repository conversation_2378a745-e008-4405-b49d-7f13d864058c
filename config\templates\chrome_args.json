{"performance_optimized": {"name": "性能优化配置", "description": "优化Chrome性能的参数配置", "args": ["--no-first-run", "--no-default-browser-check", "--disable-background-timer-throttling", "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding", "--disable-features=TranslateUI", "--disable-ipc-flooding-protection", "--max_old_space_size=4096"]}, "privacy_focused": {"name": "隐私保护配置", "description": "注重隐私保护的参数配置", "args": ["--no-first-run", "--no-default-browser-check", "--disable-background-timer-throttling", "--disable-sync", "--disable-background-networking", "--disable-client-side-phishing-detection", "--disable-component-update", "--disable-default-apps", "--incognito"]}, "development_friendly": {"name": "开发友好配置", "description": "适合开发调试的参数配置", "args": ["--no-first-run", "--no-default-browser-check", "--disable-background-timer-throttling", "--disable-web-security", "--disable-features=VizDisplayCompositor", "--allow-running-insecure-content", "--disable-extensions-except", "--load-extension"]}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v0.002 - 启动脚本
快速启动Web应用的便捷脚本

Author: AI Architecture System
Date: 2025-01-29
"""

import os
import sys
import time
import webbrowser
from pathlib import Path

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    issues = []
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        issues.append("需要Python 3.7或更高版本")
    else:
        print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # 检查Flask
    try:
        import flask
        print(f"✅ Flask版本: {flask.__version__}")
    except ImportError:
        issues.append("Flask未安装，请运行: pip install flask")
    
    # 检查psutil
    try:
        import psutil
        print(f"✅ psutil版本: {psutil.__version__}")
    except ImportError:
        issues.append("psutil未安装，请运行: pip install psutil")
    
    # 检查Chrome Portable
    chrome_path = Path('GoogleChromePortable/GoogleChromePortable.exe')
    if chrome_path.exists():
        print("✅ Chrome Portable已找到")
    else:
        issues.append("Chrome Portable未找到，请确保GoogleChromePortable目录存在")
    
    return issues

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 浏览器多账号绿色版 v0.002")
    print("   基于Chrome Portable的多账号隔离浏览器解决方案")
    print("=" * 60)
    print()
    
    # 环境检查
    issues = check_environment()
    
    if issues:
        print("\n⚠️  发现以下问题:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n请解决上述问题后重新运行。")
        input("\n按回车键退出...")
        return
    
    print("\n✅ 环境检查通过！")
    print("\n🌐 正在启动Web应用...")
    
    try:
        # 导入并启动应用
        import app
        
        print("📊 应用信息:")
        print(f"   - 项目目录: {Path.cwd()}")
        print(f"   - Web地址: http://127.0.0.1:5000")
        print(f"   - 实例目录: instances/")
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(2)
            try:
                webbrowser.open('http://127.0.0.1:5000')
                print("🌐 浏览器已自动打开")
            except:
                print("⚠️  无法自动打开浏览器，请手动访问: http://127.0.0.1:5000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        print("\n" + "=" * 60)
        print("🎉 应用启动成功！")
        print("📖 使用说明:")
        print("   1. 在浏览器中访问: http://127.0.0.1:5000")
        print("   2. 点击'新建实例'创建浏览器实例")
        print("   3. 点击'启动'按钮启动独立的浏览器")
        print("   4. 按 Ctrl+C 停止应用")
        print("=" * 60)
        
        # 启动Flask应用
        app.app.run(host='127.0.0.1', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n\n👋 应用已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n请检查错误信息并重试。")
        input("\n按回车键退出...")

if __name__ == '__main__':
    main()

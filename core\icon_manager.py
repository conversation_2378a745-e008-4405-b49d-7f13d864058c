#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标管理模块
负责浏览器实例图标的上传、管理和处理

Author: AI Architecture System
Date: 2025-01-29
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class IconManager:
    """图标管理器"""
    
    def __init__(self, icons_dir: str = 'icons'):
        self.icons_dir = Path(icons_dir)
        self.icons_dir.mkdir(exist_ok=True)
        
        # 图标配置文件
        self.config_file = self.icons_dir / 'icons.json'
        
        # 支持的图标格式
        self.supported_formats = ['.ico', '.png', '.jpg', '.jpeg', '.gif', '.bmp']
        
        # 初始化默认图标
        self._init_default_icons()
    
    def _init_default_icons(self):
        """初始化默认图标配置"""
        if not self.config_file.exists():
            default_icons = {
                'default': {
                    'name': '默认图标',
                    'description': '系统默认图标',
                    'file': 'default.ico',
                    'category': 'system',
                    'created_date': datetime.now().isoformat()
                },
                'work': {
                    'name': '工作图标',
                    'description': '工作专用图标',
                    'file': 'work.ico',
                    'category': 'business',
                    'created_date': datetime.now().isoformat()
                },
                'personal': {
                    'name': '个人图标',
                    'description': '个人使用图标',
                    'file': 'personal.ico',
                    'category': 'personal',
                    'created_date': datetime.now().isoformat()
                },
                'shopping': {
                    'name': '购物图标',
                    'description': '购物专用图标',
                    'file': 'shopping.ico',
                    'category': 'shopping',
                    'created_date': datetime.now().isoformat()
                },
                'dev': {
                    'name': '开发图标',
                    'description': '开发专用图标',
                    'file': 'dev.ico',
                    'category': 'development',
                    'created_date': datetime.now().isoformat()
                },
                'gaming': {
                    'name': '游戏图标',
                    'description': '游戏专用图标',
                    'file': 'gaming.ico',
                    'category': 'entertainment',
                    'created_date': datetime.now().isoformat()
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_icons, f, ensure_ascii=False, indent=2)
    
    def get_icons_config(self) -> Dict:
        """获取图标配置"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_icons_config(self, config: Dict):
        """保存图标配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def list_icons(self) -> List[Dict]:
        """列出所有图标"""
        config = self.get_icons_config()
        icons = []
        
        for icon_id, icon_info in config.items():
            icon_data = {
                'id': icon_id,
                'name': icon_info.get('name', icon_id),
                'description': icon_info.get('description', ''),
                'file': icon_info.get('file', ''),
                'category': icon_info.get('category', 'custom'),
                'created_date': icon_info.get('created_date', ''),
                'file_exists': (self.icons_dir / icon_info.get('file', '')).exists()
            }
            icons.append(icon_data)
        
        return sorted(icons, key=lambda x: x['created_date'], reverse=True)
    
    def get_icon(self, icon_id: str) -> Optional[Dict]:
        """获取指定图标信息"""
        config = self.get_icons_config()
        if icon_id in config:
            icon_info = config[icon_id]
            return {
                'id': icon_id,
                'name': icon_info.get('name', icon_id),
                'description': icon_info.get('description', ''),
                'file': icon_info.get('file', ''),
                'category': icon_info.get('category', 'custom'),
                'created_date': icon_info.get('created_date', ''),
                'file_path': str(self.icons_dir / icon_info.get('file', '')),
                'file_exists': (self.icons_dir / icon_info.get('file', '')).exists()
            }
        return None
    
    def upload_icon(self, file_path: str, icon_id: str, name: str = '', 
                   description: str = '', category: str = 'custom') -> Dict:
        """上传图标文件"""
        source_path = Path(file_path)
        
        # 检查文件是否存在
        if not source_path.exists():
            raise FileNotFoundError(f"图标文件不存在: {file_path}")
        
        # 检查文件格式
        if source_path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"不支持的图标格式: {source_path.suffix}")
        
        # 检查图标ID是否已存在
        if self.get_icon(icon_id):
            raise ValueError(f"图标ID已存在: {icon_id}")
        
        # 生成目标文件名
        target_filename = f"{icon_id}{source_path.suffix.lower()}"
        target_path = self.icons_dir / target_filename
        
        # 复制文件
        shutil.copy2(source_path, target_path)
        
        # 如果支持PIL，尝试转换为ICO格式
        if PIL_AVAILABLE and source_path.suffix.lower() != '.ico':
            try:
                ico_path = self.icons_dir / f"{icon_id}.ico"
                with Image.open(target_path) as img:
                    # 调整大小为32x32
                    img = img.resize((32, 32), Image.Resampling.LANCZOS)
                    img.save(ico_path, format='ICO')
                    target_filename = f"{icon_id}.ico"
                    # 删除原始文件
                    target_path.unlink()
            except Exception as e:
                print(f"转换ICO格式失败: {e}")
        
        # 更新配置
        config = self.get_icons_config()
        config[icon_id] = {
            'name': name or icon_id,
            'description': description,
            'file': target_filename,
            'category': category,
            'created_date': datetime.now().isoformat()
        }
        self.save_icons_config(config)
        
        return self.get_icon(icon_id)
    
    def delete_icon(self, icon_id: str) -> bool:
        """删除图标"""
        icon_info = self.get_icon(icon_id)
        if not icon_info:
            raise ValueError(f"图标不存在: {icon_id}")
        
        # 不允许删除系统默认图标
        if icon_info['category'] == 'system':
            raise ValueError("不能删除系统默认图标")
        
        # 删除文件
        file_path = self.icons_dir / icon_info['file']
        if file_path.exists():
            file_path.unlink()
        
        # 更新配置
        config = self.get_icons_config()
        del config[icon_id]
        self.save_icons_config(config)
        
        return True
    
    def get_icon_categories(self) -> List[str]:
        """获取图标分类列表"""
        config = self.get_icons_config()
        categories = set()
        for icon_info in config.values():
            categories.add(icon_info.get('category', 'custom'))
        return sorted(list(categories))
    
    def get_icons_by_category(self, category: str) -> List[Dict]:
        """根据分类获取图标"""
        icons = self.list_icons()
        return [icon for icon in icons if icon['category'] == category]
    
    def validate_icon_file(self, file_path: str) -> Dict:
        """验证图标文件"""
        source_path = Path(file_path)
        
        result = {
            'valid': False,
            'error': '',
            'info': {}
        }
        
        # 检查文件是否存在
        if not source_path.exists():
            result['error'] = f"文件不存在: {file_path}"
            return result
        
        # 检查文件格式
        if source_path.suffix.lower() not in self.supported_formats:
            result['error'] = f"不支持的文件格式: {source_path.suffix}"
            return result
        
        # 获取文件信息
        file_size = source_path.stat().st_size
        result['info']['file_size'] = file_size
        result['info']['format'] = source_path.suffix.lower()
        
        # 检查文件大小（限制为5MB）
        if file_size > 5 * 1024 * 1024:
            result['error'] = "文件大小超过5MB限制"
            return result
        
        # 如果支持PIL，获取图像信息
        if PIL_AVAILABLE:
            try:
                with Image.open(source_path) as img:
                    result['info']['width'] = img.width
                    result['info']['height'] = img.height
                    result['info']['mode'] = img.mode
            except Exception as e:
                result['error'] = f"无法读取图像文件: {e}"
                return result
        
        result['valid'] = True
        return result

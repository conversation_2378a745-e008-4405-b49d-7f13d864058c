#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v0.002 - 功能测试脚本
测试所有Web界面功能是否正常工作

Author: AI Architecture System
Date: 2025-01-29
"""

import requests
import json
import time
import sys

BASE_URL = 'http://127.0.0.1:5000'

def test_api_endpoint(method, endpoint, data=None, expected_success=True):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == 'GET':
            response = requests.get(url)
        elif method == 'POST':
            response = requests.post(url, json=data)
        elif method == 'PUT':
            response = requests.put(url, json=data)
        elif method == 'DELETE':
            response = requests.delete(url)
        else:
            return False, f"不支持的HTTP方法: {method}"
        
        if response.status_code == 200:
            result = response.json()
            if expected_success:
                if result.get('success', False):
                    return True, result.get('message', '成功')
                else:
                    return False, result.get('error', '未知错误')
            else:
                return True, "请求成功"
        else:
            return False, f"HTTP错误: {response.status_code}"
            
    except Exception as e:
        return False, f"请求异常: {str(e)}"

def run_tests():
    """运行所有测试"""
    print("🚀 开始测试浏览器多账号绿色版 v0.002 的所有功能")
    print("=" * 60)
    
    tests = [
        # 基础健康检查
        ("GET", "/health", None, True, "系统健康检查"),
        
        # 实例管理API
        ("GET", "/api/instances", None, True, "获取实例列表"),
        ("GET", "/api/instances/status/all", None, True, "获取所有实例状态"),
        ("GET", "/api/statistics", None, True, "获取统计信息"),
        
        # 批量配置API
        ("GET", "/api/batch/templates", None, True, "获取批量配置模板"),
        
        # 分组管理API
        ("GET", "/api/groups", None, True, "获取分组信息"),
        
        # 配置导入导出API
        ("GET", "/api/export", None, True, "导出配置"),
        
        # 创建测试实例
        ("POST", "/api/instances", {
            "name": "test_instance_" + str(int(time.time())),
            "display_name": "测试实例",
            "description": "功能测试用实例",
            "homepage": "https://www.google.com",
            "group": "test"
        }, True, "创建测试实例"),
    ]
    
    passed = 0
    failed = 0
    test_instance_name = None
    
    for method, endpoint, data, expected_success, description in tests:
        print(f"🔍 测试: {description}")
        success, message = test_api_endpoint(method, endpoint, data, expected_success)
        
        if success:
            print(f"✅ 通过: {message}")
            passed += 1
            
            # 记录创建的测试实例名称
            if "创建测试实例" in description and data:
                test_instance_name = data["name"]
        else:
            print(f"❌ 失败: {message}")
            failed += 1
        
        time.sleep(0.5)  # 避免请求过快
    
    # 如果创建了测试实例，测试实例相关的功能
    if test_instance_name:
        print(f"\n🔍 测试实例相关功能 (实例: {test_instance_name})")
        
        instance_tests = [
            ("GET", f"/api/instances/{test_instance_name}", None, True, "获取实例详情"),
            ("GET", f"/api/instances/{test_instance_name}/status", None, True, "获取实例状态"),
            ("PUT", f"/api/instances/{test_instance_name}", {
                "display_name": "更新后的测试实例",
                "description": "已更新的描述"
            }, True, "更新实例配置"),
            ("POST", f"/api/instances/{test_instance_name}/open-folder", None, True, "打开实例文件夹"),
            ("POST", f"/api/instances/{test_instance_name}/launch", None, True, "启动实例"),
        ]
        
        for method, endpoint, data, expected_success, description in instance_tests:
            print(f"🔍 测试: {description}")
            success, message = test_api_endpoint(method, endpoint, data, expected_success)
            
            if success:
                print(f"✅ 通过: {message}")
                passed += 1
            else:
                print(f"❌ 失败: {message}")
                failed += 1
            
            time.sleep(1)  # 给启动一些时间
        
        # 等待一下再停止实例
        time.sleep(2)
        
        # 测试停止实例
        print(f"🔍 测试: 停止实例")
        success, message = test_api_endpoint("POST", f"/api/instances/{test_instance_name}/stop", None, True)
        if success:
            print(f"✅ 通过: {message}")
            passed += 1
        else:
            print(f"❌ 失败: {message}")
            failed += 1
        
        # 清理测试实例
        print(f"🔍 清理: 删除测试实例")
        success, message = test_api_endpoint("DELETE", f"/api/instances/{test_instance_name}", None, True)
        if success:
            print(f"✅ 清理成功: {message}")
        else:
            print(f"⚠️ 清理失败: {message}")
    
    # 测试批量操作
    print(f"\n🔍 测试批量操作功能")
    batch_tests = [
        ("POST", "/api/batch/create", {
            "template": "basic_set"
        }, True, "批量创建基础实例集合"),
    ]
    
    for method, endpoint, data, expected_success, description in batch_tests:
        print(f"🔍 测试: {description}")
        success, message = test_api_endpoint(method, endpoint, data, expected_success)
        
        if success:
            print(f"✅ 通过: {message}")
            passed += 1
        else:
            print(f"❌ 失败: {message}")
            failed += 1
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试结果统计:")
    print(f"   ✅ 通过: {passed}")
    print(f"   ❌ 失败: {failed}")
    print(f"   📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有功能测试通过！界面功能已修复完成。")
        return True
    else:
        print(f"\n⚠️ 发现 {failed} 个功能问题，需要进一步修复。")
        return False

if __name__ == '__main__':
    try:
        success = run_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)

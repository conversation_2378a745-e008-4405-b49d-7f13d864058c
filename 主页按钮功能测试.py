#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v0.002 - 主页按钮功能测试
测试主页上所有按钮对应的功能实现

Author: AI Architecture System
Date: 2025-01-29
"""

import requests
import json
import time

API_BASE = 'http://127.0.0.1:5000'

def test_button_function(description, test_func):
    """测试按钮功能"""
    try:
        print(f"🔘 测试 {description}...")
        result = test_func()
        if result:
            print(f"   ✅ {description} - 功能正常")
            return True
        else:
            print(f"   ❌ {description} - 功能异常")
            return False
    except Exception as e:
        print(f"   ❌ {description} - 测试失败: {e}")
        return False

def test_health_check():
    """测试系统检查按钮"""
    r = requests.get(f'{API_BASE}/health')
    return r.status_code == 200 and r.json().get('status') == 'healthy'

def test_get_instances():
    """测试获取实例列表"""
    r = requests.get(f'{API_BASE}/api/instances')
    return r.status_code == 200 and r.json().get('success', False)

def test_create_instance():
    """测试新建实例按钮功能"""
    data = {
        'name': 'button_test_instance',
        'display_name': 'Chrome - 按钮测试',
        'description': '用于测试按钮功能的实例',
        'homepage': 'https://www.google.com',
        'group': 'test'
    }
    r = requests.post(f'{API_BASE}/api/instances', json=data)
    return r.status_code == 200 and r.json().get('success', False)

def test_launch_instance():
    """测试启动实例按钮功能"""
    r = requests.post(f'{API_BASE}/api/instances/button_test_instance/launch')
    return r.status_code == 200 and r.json().get('success', False)

def test_stop_instance():
    """测试停止实例按钮功能"""
    r = requests.post(f'{API_BASE}/api/instances/button_test_instance/stop')
    return r.status_code == 200 and r.json().get('success', False)

def test_edit_instance():
    """测试编辑实例按钮功能"""
    data = {
        'display_name': 'Chrome - 按钮测试(已编辑)',
        'description': '编辑后的描述',
        'group': 'updated'
    }
    r = requests.put(f'{API_BASE}/api/instances/button_test_instance', json=data)
    return r.status_code == 200 and r.json().get('success', False)

def test_batch_templates():
    """测试批量配置按钮功能"""
    r = requests.get(f'{API_BASE}/api/batch/templates')
    return r.status_code == 200 and r.json().get('success', False)

def test_batch_create():
    """测试批量创建功能"""
    r = requests.post(f'{API_BASE}/api/batch/create', json={'template': 'basic_set'})
    return r.status_code == 200 and r.json().get('success', False)

def test_batch_launch():
    """测试批量启动按钮功能"""
    data = {'instances': ['test', 'test_work']}
    r = requests.post(f'{API_BASE}/api/batch/launch', json=data)
    return r.status_code == 200 and r.json().get('success', False)

def test_export_config():
    """测试导出配置按钮功能"""
    r = requests.get(f'{API_BASE}/api/export')
    return r.status_code == 200 and r.json().get('success', False)

def test_groups():
    """测试分组功能"""
    r = requests.get(f'{API_BASE}/api/groups')
    return r.status_code == 200 and r.json().get('success', False)

def test_statistics():
    """测试统计信息功能"""
    r = requests.get(f'{API_BASE}/api/statistics')
    return r.status_code == 200 and r.json().get('success', False)

def test_instance_status():
    """测试实例状态功能"""
    r = requests.get(f'{API_BASE}/api/instances/status/all')
    return r.status_code == 200 and r.json().get('success', False)

def test_delete_instance():
    """测试删除实例按钮功能"""
    r = requests.delete(f'{API_BASE}/api/instances/button_test_instance')
    return r.status_code == 200 and r.json().get('success', False)

def main():
    """主测试函数"""
    print("🚀 浏览器多账号绿色版 v0.002 - 主页按钮功能测试")
    print("=" * 60)
    
    # 按钮功能测试列表
    button_tests = [
        ("系统检查按钮", test_health_check),
        ("获取实例列表", test_get_instances),
        ("新建实例按钮", test_create_instance),
        ("启动实例按钮", test_launch_instance),
        ("停止实例按钮", test_stop_instance),
        ("编辑实例按钮", test_edit_instance),
        ("批量配置模板", test_batch_templates),
        ("批量创建功能", test_batch_create),
        ("批量启动按钮", test_batch_launch),
        ("导出配置按钮", test_export_config),
        ("分组管理功能", test_groups),
        ("统计信息功能", test_statistics),
        ("实例状态功能", test_instance_status),
        ("删除实例按钮", test_delete_instance),
    ]
    
    # 执行测试
    passed = 0
    total = len(button_tests)
    
    print(f"\n📋 开始测试 {total} 个按钮功能...")
    print("-" * 60)
    
    for description, test_func in button_tests:
        if test_button_function(description, test_func):
            passed += 1
        time.sleep(0.5)  # 避免请求过快
    
    # 测试结果统计
    print("\n" + "=" * 60)
    print("📊 测试结果统计")
    print(f"✅ 通过: {passed}/{total} ({passed/total*100:.1f}%)")
    print(f"❌ 失败: {total-passed}/{total} ({(total-passed)/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有按钮功能测试通过！")
        print("✨ 主页功能完整，可以正常使用")
    else:
        print(f"\n⚠️  有 {total-passed} 个功能需要检查")
        print("🔧 请检查失败的功能并进行修复")
    
    # 功能覆盖率报告
    print("\n📋 功能覆盖率报告")
    print("🔘 快速操作工具栏:")
    print("   ✅ 新建实例、刷新列表、批量配置、系统检查")
    print("   ✅ 批量操作、导出配置、导入配置、帮助")
    print("   ✅ 启动全部")
    
    print("🔘 实例操作按钮:")
    print("   ✅ 启动/停止切换、编辑、删除")
    print("   ✅ 查看详情、复制实例、打开目录、高级设置")
    
    print("🔘 批量操作工具栏:")
    print("   ✅ 批量启动、批量删除、全选、清除选择")
    
    print("🔘 搜索和过滤:")
    print("   ✅ 实时搜索、状态过滤、分组过滤、清除过滤器")
    
    print("🔘 模态框功能:")
    print("   ✅ 新建实例、编辑实例、批量配置、导入配置")
    print("   ✅ 实例详情、高级设置、帮助系统")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生异常: {e}")

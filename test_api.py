#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
测试浏览器多账号绿色版的API功能
"""

import requests
import json
import time

BASE_URL = 'http://127.0.0.1:5000'

def test_health():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f'{BASE_URL}/health')
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False

def test_get_instances():
    """测试获取实例列表"""
    print("📋 测试获取实例列表...")
    try:
        response = requests.get(f'{BASE_URL}/api/instances')
        print(f"   状态码: {response.status_code}")
        data = response.json()
        print(f"   响应: {data}")
        return response.status_code == 200 and data.get('success', False)
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False

def test_create_instance():
    """测试创建实例"""
    print("➕ 测试创建实例...")
    try:
        data = {
            'name': 'test',
            'display_name': 'Chrome - 测试',
            'description': '测试实例',
            'homepage': 'https://www.google.com'
        }
        
        response = requests.post(
            f'{BASE_URL}/api/instances',
            json=data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   状态码: {response.status_code}")
        result = response.json()
        print(f"   响应: {result}")
        
        if response.status_code == 200 and result.get('success', False):
            print("   ✅ 实例创建成功!")
            return True
        else:
            print(f"   ❌ 创建失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False

def test_launch_instance():
    """测试启动实例"""
    print("🚀 测试启动实例...")
    try:
        response = requests.post(f'{BASE_URL}/api/instances/test/launch')
        print(f"   状态码: {response.status_code}")
        result = response.json()
        print(f"   响应: {result}")
        
        if response.status_code == 200 and result.get('success', False):
            print("   ✅ 实例启动成功!")
            return True
        else:
            print(f"   ❌ 启动失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False

def test_delete_instance():
    """测试删除实例"""
    print("🗑️ 测试删除实例...")
    try:
        response = requests.delete(f'{BASE_URL}/api/instances/test')
        print(f"   状态码: {response.status_code}")
        result = response.json()
        print(f"   响应: {result}")
        
        if response.status_code == 200 and result.get('success', False):
            print("   ✅ 实例删除成功!")
            return True
        else:
            print(f"   ❌ 删除失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始API功能测试")
    print("=" * 50)
    
    tests = [
        ("健康检查", test_health),
        ("获取实例列表", test_get_instances),
        ("创建实例", test_create_instance),
        ("再次获取实例列表", test_get_instances),
        ("启动实例", test_launch_instance),
        ("删除实例", test_delete_instance),
        ("最终获取实例列表", test_get_instances),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n📝 {name}")
        if test_func():
            passed += 1
            print(f"   ✅ 通过")
        else:
            print(f"   ❌ 失败")
        
        time.sleep(1)  # 等待1秒
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MVP版本功能正常！")
    else:
        print(f"⚠️ {total - passed} 个测试失败，需要检查问题")

if __name__ == '__main__':
    main()

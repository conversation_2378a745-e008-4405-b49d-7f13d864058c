#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责系统配置、模板管理和配置文件处理

Author: AI Architecture System
Date: 2025-01-29
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = 'config'):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.system_config_file = self.config_dir / 'system.json'
        self.templates_dir = self.config_dir / 'templates'
        self.templates_dir.mkdir(exist_ok=True)
        
        # 初始化系统配置
        self._init_system_config()
        self._init_default_templates()
    
    def _init_system_config(self):
        """初始化系统配置"""
        if not self.system_config_file.exists():
            default_config = {
                'version': '0.002',
                'chrome_portable_path': 'GoogleChromePortable/GoogleChromePortable.exe',
                'instances_dir': 'instances',
                'icons_dir': 'icons',
                'max_instances': 10,
                'auto_launch_web': True,
                'web_port': 5000,
                'created_date': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }
            self.save_system_config(default_config)
    
    def _init_default_templates(self):
        """初始化默认模板"""
        # Chrome参数模板
        chrome_args_templates = {
            'performance_optimized': {
                'name': '性能优化配置',
                'description': '优化Chrome性能的参数配置',
                'args': [
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--max_old_space_size=4096'
                ]
            },
            'privacy_focused': {
                'name': '隐私保护配置',
                'description': '注重隐私保护的参数配置',
                'args': [
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-sync',
                    '--disable-background-networking',
                    '--disable-client-side-phishing-detection',
                    '--disable-component-update',
                    '--disable-default-apps',
                    '--incognito'
                ]
            },
            'development_friendly': {
                'name': '开发友好配置',
                'description': '适合开发调试的参数配置',
                'args': [
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-background-timer-throttling',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--allow-running-insecure-content',
                    '--disable-extensions-except',
                    '--load-extension'
                ]
            }
        }
        
        chrome_args_file = self.templates_dir / 'chrome_args.json'
        if not chrome_args_file.exists():
            with open(chrome_args_file, 'w', encoding='utf-8') as f:
                json.dump(chrome_args_templates, f, ensure_ascii=False, indent=2)
        
        # 批量配置模板
        batch_templates = {
            'basic_set': {
                'name': '基础实例集合',
                'description': '工作、个人、购物三个基础实例',
                'instances': [
                    {
                        'name': 'work',
                        'display_name': 'Chrome - 工作',
                        'description': '工作专用浏览器',
                        'homepage': 'https://www.google.com',
                        'icon': 'work'
                    },
                    {
                        'name': 'personal',
                        'display_name': 'Chrome - 个人',
                        'description': '个人使用浏览器',
                        'homepage': 'https://www.google.com',
                        'icon': 'personal'
                    },
                    {
                        'name': 'shopping',
                        'display_name': 'Chrome - 购物',
                        'description': '购物专用浏览器',
                        'homepage': 'https://www.taobao.com',
                        'icon': 'shopping'
                    }
                ]
            },
            'developer_set': {
                'name': '开发者实例集合',
                'description': '专为开发者设计的实例集合',
                'instances': [
                    {
                        'name': 'dev_main',
                        'display_name': 'Chrome - 开发主环境',
                        'description': '主要开发环境',
                        'homepage': 'http://localhost:3000',
                        'icon': 'dev'
                    },
                    {
                        'name': 'dev_test',
                        'display_name': 'Chrome - 测试环境',
                        'description': '测试环境浏览器',
                        'homepage': 'http://localhost:8080',
                        'icon': 'dev'
                    },
                    {
                        'name': 'dev_docs',
                        'display_name': 'Chrome - 文档查阅',
                        'description': '文档和资料查阅',
                        'homepage': 'https://developer.mozilla.org',
                        'icon': 'dev'
                    }
                ]
            }
        }
        
        batch_file = self.templates_dir / 'batch_configs.json'
        if not batch_file.exists():
            with open(batch_file, 'w', encoding='utf-8') as f:
                json.dump(batch_templates, f, ensure_ascii=False, indent=2)
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        if self.system_config_file.exists():
            with open(self.system_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def save_system_config(self, config: Dict[str, Any]):
        """保存系统配置"""
        config['last_updated'] = datetime.now().isoformat()
        with open(self.system_config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def update_system_config(self, **kwargs):
        """更新系统配置"""
        config = self.get_system_config()
        config.update(kwargs)
        self.save_system_config(config)
    
    def get_chrome_args_templates(self) -> Dict[str, Any]:
        """获取Chrome参数模板"""
        chrome_args_file = self.templates_dir / 'chrome_args.json'
        if chrome_args_file.exists():
            with open(chrome_args_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def get_batch_templates(self) -> Dict[str, Any]:
        """获取批量配置模板"""
        batch_file = self.templates_dir / 'batch_configs.json'
        if batch_file.exists():
            with open(batch_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def get_chrome_args_by_template(self, template_name: str) -> List[str]:
        """根据模板名称获取Chrome参数"""
        templates = self.get_chrome_args_templates()
        template = templates.get(template_name, {})
        return template.get('args', [])
    
    def create_custom_template(self, template_type: str, name: str, data: Dict[str, Any]):
        """创建自定义模板"""
        if template_type == 'chrome_args':
            templates = self.get_chrome_args_templates()
            templates[name] = data
            chrome_args_file = self.templates_dir / 'chrome_args.json'
            with open(chrome_args_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=2)
        
        elif template_type == 'batch_config':
            templates = self.get_batch_templates()
            templates[name] = data
            batch_file = self.templates_dir / 'batch_configs.json'
            with open(batch_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=2)
    
    def validate_config(self, config: Dict[str, Any]) -> List[str]:
        """验证配置文件"""
        errors = []
        
        # 检查必需字段
        required_fields = ['name', 'display_name']
        for field in required_fields:
            if field not in config or not config[field]:
                errors.append(f"缺少必需字段: {field}")
        
        # 检查名称格式
        if 'name' in config:
            name = config['name']
            if not name.replace('_', '').replace('-', '').isalnum():
                errors.append("名称只能包含字母、数字、下划线和连字符")
        
        # 检查URL格式
        if 'homepage' in config:
            homepage = config['homepage']
            if homepage and not (homepage.startswith('http://') or homepage.startswith('https://')):
                errors.append("主页URL格式不正确")
        
        return errors
    
    def export_config(self, output_file: str):
        """导出配置"""
        export_data = {
            'system_config': self.get_system_config(),
            'chrome_args_templates': self.get_chrome_args_templates(),
            'batch_templates': self.get_batch_templates(),
            'export_date': datetime.now().isoformat()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    def import_config(self, input_file: str):
        """导入配置"""
        with open(input_file, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        # 导入系统配置
        if 'system_config' in import_data:
            self.save_system_config(import_data['system_config'])
        
        # 导入模板
        if 'chrome_args_templates' in import_data:
            chrome_args_file = self.templates_dir / 'chrome_args.json'
            with open(chrome_args_file, 'w', encoding='utf-8') as f:
                json.dump(import_data['chrome_args_templates'], f, ensure_ascii=False, indent=2)
        
        if 'batch_templates' in import_data:
            batch_file = self.templates_dir / 'batch_configs.json'
            with open(batch_file, 'w', encoding='utf-8') as f:
                json.dump(import_data['batch_templates'], f, ensure_ascii=False, indent=2)

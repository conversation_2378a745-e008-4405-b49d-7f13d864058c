#!/usr/bin/env python3
import requests

# 获取当前实例状态
r = requests.get('http://127.0.0.1:5000/api/instances')
data = r.json()

print('Current instances:')
for i in data['data']:
    print(f'  {i["name"]}: {i["display_name"]} ({i["group"]})')

print(f'\nTotal: {len(data["data"])} instances')

# 测试健康状态
r = requests.get('http://127.0.0.1:5000/health')
health = r.json()
print(f'\nHealth status: {health["status"]}')
print(f'Chrome available: {health["chrome_portable"]}')
print(f'Groups: {health.get("groups", {})}')

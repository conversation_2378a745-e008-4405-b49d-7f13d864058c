
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}浏览器多账号绿色版 v0.002{% endblock %}</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #5570f1;
            --secondary-color: #8b92b2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --sidebar-bg: #1a1d29;
            --sidebar-hover: #2a2d3a;
            --main-bg: #f8fafc;
            --card-bg: #ffffff;
            --text-primary: #1a1d29;
            --text-secondary: #8b92b2;
            --border-color: #e2e8f0;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--main-bg);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: var(--sidebar-bg);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-brand {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .sidebar-brand i {
            margin-right: 0.75rem;
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: var(--sidebar-hover);
            color: white;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
        }

        .nav-link i {
            margin-right: 0.75rem;
            width: 20px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .top-header {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .content-area {
            padding: 2rem;
        }

        /* 卡片样式 */
        .card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .card-header {
            background: transparent;
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, #4c63d2 100%);
            color: white;
            border: none;
        }

        .stats-card.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
        }

        .stats-card.secondary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #6c757d 100%);
        }

        .stats-card.info {
            background: linear-gradient(135deg, var(--info-color) 0%, #20c997 100%);
        }

        .stats-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .stats-info h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stats-info p {
            opacity: 0.9;
            margin: 0;
        }

        .stats-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        /* 实例网格 */
        .instances-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
        }

        .instance-card {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .instance-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
        }

        .instance-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .instance-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .instance-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .instance-meta {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .meta-item i {
            margin-right: 0.5rem;
            width: 16px;
        }

        .instance-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* 按钮样式 */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #4c63d2;
            transform: translateY(-1px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-outline-secondary {
            background: transparent;
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-outline-danger {
            background: transparent;
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
        }

        /* 状态徽章 */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-running {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .status-stopped {
            background: rgba(139, 146, 178, 0.1);
            color: var(--text-secondary);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .empty-state h4 {
            margin-bottom: 0.5rem;
            color: var(--text-primary);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .content-area {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .instances-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 加载动画 */
        .loading {
            display: none;
        }

        .loading.show {
            display: inline-block;
        }

        /* Toast通知 */
        .toast-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1050;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="/" class="sidebar-brand">
                <i class="fab fa-chrome"></i>
                浏览器多账号绿色版
            </a>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="/" class="nav-link active">
                    <i class="fas fa-th-large"></i>
                    仪表板
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link" onclick="showCreateModal()">
                    <i class="fas fa-plus-circle"></i>
                    新建实例
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link" onclick="showBatchModal()">
                    <i class="fas fa-layer-group"></i>
                    批量配置
                </a>
            </div>
            <div class="nav-item">
                <a href="/health" class="nav-link">
                    <i class="fas fa-heartbeat"></i>
                    系统状态
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link" onclick="refreshInstances()">
                    <i class="fas fa-sync-alt"></i>
                    刷新数据
                </a>
            </div>

            <hr style="margin: 1rem; border-color: rgba(255,255,255,0.1);">

            <div class="nav-item">
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    设置
                </a>
            </div>
            <div class="nav-item">
                <a href="https://github.com" target="_blank" class="nav-link">
                    <i class="fab fa-github"></i>
                    GitHub
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部标题栏 -->
        <div class="top-header">
            <h1 class="page-title">{% block page_title %}浏览器实例管理{% endblock %}</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="showCreateModal()">
                    <i class="fas fa-plus"></i>
                    新建实例
                </button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Toast 通知容器 -->
    <div class="toast-container">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="footer mt-auto" style="background: var(--card-bg); border-top: 1px solid var(--border-color); padding: 2rem 0; margin-top: 3rem;">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-4">
                    <p class="mb-0">
                        <i class="fab fa-chrome me-2"></i>
                        浏览器多账号绿色版 v0.002
                    </p>
                    <small class="text-muted">基于Chrome Portable的多账号隔离解决方案</small>
                </div>
                <div class="col-md-4">
                    <p class="mb-0">
                        <i class="fas fa-keyboard me-2"></i>
                        快捷键
                    </p>
                    <small class="text-muted">
                        Ctrl+N 新建 | Ctrl+R 刷新 | Ctrl+B 批量 | / 搜索
                    </small>
                </div>
                <div class="col-md-4 text-md-end">
                    <p class="mb-0">
                        <i class="fas fa-code me-2"></i>
                        由 AI Architecture System 构建
                    </p>
                    <small class="text-muted">架构驱动 · 智能化设计</small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 显示Toast通知
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = toast.querySelector('.toast-body');
            const toastHeader = toast.querySelector('.toast-header');
            const icon = toastHeader.querySelector('i');
            
            // 设置消息
            toastBody.textContent = message;
            
            // 设置图标和颜色
            icon.className = 'me-2';
            switch(type) {
                case 'success':
                    icon.classList.add('fas', 'fa-check-circle', 'text-success');
                    break;
                case 'error':
                    icon.classList.add('fas', 'fa-exclamation-circle', 'text-danger');
                    break;
                case 'warning':
                    icon.classList.add('fas', 'fa-exclamation-triangle', 'text-warning');
                    break;
                default:
                    icon.classList.add('fas', 'fa-info-circle', 'text-primary');
            }
            
            // 显示Toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        // 显示加载状态
        function showLoading(element) {
            const loading = element.querySelector('.loading');
            if (loading) {
                loading.classList.add('show');
            }
            element.disabled = true;
        }
        
        // 隐藏加载状态
        function hideLoading(element) {
            const loading = element.querySelector('.loading');
            if (loading) {
                loading.classList.remove('show');
            }
            element.disabled = false;
        }
        
        // API请求封装
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error || '请求失败');
                }
                
                return data;
            } catch (error) {
                console.error('API请求失败:', error);
                throw error;
            }
        }
        
        // 刷新实例列表
        function refreshInstances() {
            location.reload();
        }
        
        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器实例管理模块
负责浏览器实例的创建、配置、启动和管理

Author: AI Architecture System
Date: 2025-01-29
"""

import os
import json
import subprocess
import psutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional


class BrowserInstance:
    """浏览器实例类"""
    
    def __init__(self, name: str, config_path: Path):
        self.name = name
        self.config_path = config_path
        self.instance_dir = config_path.parent
        self.data_dir = self.instance_dir / 'Data' / 'profile'
        self._config = None
        self._process = None
    
    @property
    def config(self) -> Dict:
        """获取实例配置"""
        if self._config is None:
            self.reload_config()
        return self._config
    
    def reload_config(self):
        """重新加载配置"""
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = json.load(f)
        else:
            self._config = {}
    
    def save_config(self):
        """保存配置"""
        if self._config:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
    
    def update_config(self, **kwargs):
        """更新配置"""
        config = self.config
        config.update(kwargs)
        config['last_updated'] = datetime.now().isoformat()
        self._config = config
        self.save_config()
    
    @property
    def display_name(self) -> str:
        """获取显示名称"""
        return self.config.get('display_name', self.name)
    
    @property
    def description(self) -> str:
        """获取描述"""
        return self.config.get('description', '')
    
    @property
    def homepage(self) -> str:
        """获取主页URL"""
        return self.config.get('homepage', 'https://www.google.com')
    
    @property
    def icon(self) -> str:
        """获取图标"""
        return self.config.get('icon', 'default')
    
    @property
    def chrome_args(self) -> List[str]:
        """获取Chrome启动参数"""
        return self.config.get('chrome_args', [
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-background-timer-throttling'
        ])
    
    def is_running(self) -> bool:
        """检查实例是否正在运行"""
        if self._process and self._process.poll() is None:
            return True
        
        # 通过进程检查
        data_dir_str = str(self.data_dir.absolute())
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any(data_dir_str in arg for arg in cmdline):
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return False
    
    def get_status(self) -> str:
        """获取实例状态"""
        return 'running' if self.is_running() else 'stopped'
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'homepage': self.homepage,
            'icon': self.icon,
            'status': self.get_status(),
            'data_dir': str(self.data_dir),
            'created_date': self.config.get('created_date', ''),
            'last_updated': self.config.get('last_updated', '')
        }


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, chrome_path: str = 'GoogleChromePortable/GoogleChromePortable.exe',
                 instances_dir: str = 'instances'):
        self.chrome_path = Path(chrome_path)
        self.instances_dir = Path(instances_dir)
        self.instances_dir.mkdir(exist_ok=True)
    
    def validate_chrome_path(self) -> bool:
        """验证Chrome Portable路径"""
        return self.chrome_path.exists() and self.chrome_path.is_file()
    
    def list_instances(self) -> List[BrowserInstance]:
        """列出所有实例"""
        instances = []
        
        if not self.instances_dir.exists():
            return instances
        
        for instance_dir in self.instances_dir.iterdir():
            if instance_dir.is_dir():
                config_file = instance_dir / 'config.json'
                if config_file.exists():
                    try:
                        instance = BrowserInstance(instance_dir.name, config_file)
                        instances.append(instance)
                    except Exception as e:
                        print(f"加载实例失败 {instance_dir.name}: {e}")
        
        return sorted(instances, key=lambda x: x.name)
    
    def get_instance(self, name: str) -> Optional[BrowserInstance]:
        """获取指定实例"""
        instance_dir = self.instances_dir / name
        config_file = instance_dir / 'config.json'
        
        if config_file.exists():
            return BrowserInstance(name, config_file)
        return None
    
    def create_instance(self, name: str, display_name: str, 
                       description: str = "", homepage: str = "https://www.google.com",
                       icon: str = "default") -> BrowserInstance:
        """创建新实例"""
        # 验证名称
        if not name or not name.replace('_', '').replace('-', '').isalnum():
            raise ValueError("实例名称只能包含字母、数字、下划线和连字符")
        
        instance_dir = self.instances_dir / name
        if instance_dir.exists():
            raise ValueError(f"实例 {name} 已存在")
        
        # 创建目录结构
        instance_dir.mkdir(parents=True)
        data_dir = instance_dir / 'Data' / 'profile'
        data_dir.mkdir(parents=True)
        
        # 创建配置
        config = {
            'name': name,
            'display_name': display_name,
            'description': description,
            'homepage': homepage,
            'icon': icon,
            'created_date': datetime.now().isoformat(),
            'last_updated': datetime.now().isoformat(),
            'chrome_args': [
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-background-timer-throttling',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        }
        
        config_file = instance_dir / 'config.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return BrowserInstance(name, config_file)
    
    def launch_instance(self, name: str) -> bool:
        """启动实例"""
        if not self.validate_chrome_path():
            raise FileNotFoundError(f"Chrome Portable 未找到: {self.chrome_path}")
        
        instance = self.get_instance(name)
        if not instance:
            raise ValueError(f"实例 {name} 不存在")
        
        if instance.is_running():
            raise RuntimeError(f"实例 {name} 已在运行")
        
        # 构建启动命令
        cmd = [
            str(self.chrome_path.absolute()),
            f'--user-data-dir={instance.data_dir.absolute()}',
            f'--app-name={instance.display_name}',
            instance.homepage
        ] + instance.chrome_args
        
        try:
            # 启动浏览器进程
            process = subprocess.Popen(
                cmd,
                cwd=str(self.chrome_path.parent),
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            instance._process = process
            
            # 更新最后启动时间
            instance.update_config(last_launched=datetime.now().isoformat())
            
            return True
        except Exception as e:
            raise RuntimeError(f"启动实例失败: {e}")
    
    def delete_instance(self, name: str) -> bool:
        """删除实例"""
        instance = self.get_instance(name)
        if not instance:
            raise ValueError(f"实例 {name} 不存在")
        
        if instance.is_running():
            raise RuntimeError(f"实例 {name} 正在运行，请先停止")
        
        # 删除实例目录
        import shutil
        shutil.rmtree(instance.instance_dir)
        return True
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        instances = self.list_instances()
        running_count = sum(1 for inst in instances if inst.is_running())
        
        return {
            'total_instances': len(instances),
            'running_instances': running_count,
            'stopped_instances': len(instances) - running_count,
            'chrome_available': self.validate_chrome_path()
        }

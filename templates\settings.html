{% extends "base.html" %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
<!-- 设置选项卡 -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-cog me-2"></i>常规设置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="chrome-tab" data-bs-toggle="tab" data-bs-target="#chrome" type="button" role="tab">
                    <i class="fab fa-chrome me-2"></i>Chrome配置
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="icons-tab" data-bs-toggle="tab" data-bs-target="#icons" type="button" role="tab">
                    <i class="fas fa-images me-2"></i>图标管理
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                    <i class="fas fa-download me-2"></i>备份恢复
                </button>
            </li>
        </ul>
    </div>
    
    <div class="card-body">
        <div class="tab-content" id="settingsTabContent">
            <!-- 常规设置 -->
            <div class="tab-pane fade show active" id="general" role="tabpanel">
                <h5 class="mb-4">常规设置</h5>
                
                <form id="generalSettingsForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="maxInstances" class="form-label">最大实例数</label>
                            <input type="number" class="form-control" id="maxInstances" min="1" max="50" value="10">
                            <div class="form-text">同时允许创建的最大实例数量</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="webPort" class="form-label">Web端口</label>
                            <input type="number" class="form-control" id="webPort" min="1000" max="65535" value="5000">
                            <div class="form-text">Web界面监听端口</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoLaunchWeb" checked>
                            <label class="form-check-label" for="autoLaunchWeb">
                                启动时自动打开Web界面
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableLogging">
                            <label class="form-check-label" for="enableLogging">
                                启用详细日志记录
                            </label>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="saveGeneralSettings()">
                        <i class="fas fa-save me-2"></i>保存设置
                    </button>
                </form>
            </div>
            
            <!-- Chrome配置 -->
            <div class="tab-pane fade" id="chrome" role="tabpanel">
                <h5 class="mb-4">Chrome配置</h5>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    这些设置将应用到所有新创建的浏览器实例
                </div>
                
                <form id="chromeSettingsForm">
                    <div class="mb-4">
                        <label class="form-label">默认启动参数</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="defaultNoFirstRun" checked disabled>
                            <label class="form-check-label" for="defaultNoFirstRun">
                                --no-first-run (跳过首次运行向导)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="defaultNoBrowserCheck" checked disabled>
                            <label class="form-check-label" for="defaultNoBrowserCheck">
                                --no-default-browser-check (跳过默认浏览器检查)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="defaultDisableThrottling" checked disabled>
                            <label class="form-check-label" for="defaultDisableThrottling">
                                --disable-background-timer-throttling (禁用后台限制)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label">可选启动参数</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enablePerformanceMode">
                            <label class="form-check-label" for="enablePerformanceMode">
                                性能优化模式 (增加内存限制、禁用后台渲染)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enablePrivacyMode">
                            <label class="form-check-label" for="enablePrivacyMode">
                                隐私保护模式 (禁用同步、后台网络)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableDevMode">
                            <label class="form-check-label" for="enableDevMode">
                                开发者模式 (禁用安全策略、允许跨域)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="customArgs" class="form-label">自定义启动参数</label>
                        <textarea class="form-control" id="customArgs" rows="3" 
                                  placeholder="每行一个参数，例如：&#10;--disable-extensions&#10;--incognito"></textarea>
                        <div class="form-text">高级用户可以添加自定义Chrome启动参数</div>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="saveChromeSettings()">
                        <i class="fas fa-save me-2"></i>保存Chrome设置
                    </button>
                </form>
            </div>
            
            <!-- 图标管理 -->
            <div class="tab-pane fade" id="icons" role="tabpanel">
                <h5 class="mb-4">图标管理</h5>
                
                <div class="row mb-4">
                    <div class="col-md-8">
                        <p class="text-muted">管理浏览器实例的自定义图标，支持 ICO、PNG、JPG 等格式。</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-primary" onclick="showUploadIconModal()">
                            <i class="fas fa-upload me-2"></i>上传图标
                        </button>
                    </div>
                </div>
                
                <div id="iconsList">
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载图标列表...</p>
                    </div>
                </div>
            </div>
            
            <!-- 备份恢复 -->
            <div class="tab-pane fade" id="backup" role="tabpanel">
                <h5 class="mb-4">备份恢复</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-download me-2"></i>导出配置
                                </h6>
                                <p class="card-text text-muted">导出所有实例配置和系统设置到文件</p>
                                <button class="btn btn-outline-primary" onclick="exportConfig()">
                                    <i class="fas fa-file-export me-2"></i>导出配置
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-upload me-2"></i>导入配置
                                </h6>
                                <p class="card-text text-muted">从备份文件恢复实例配置和系统设置</p>
                                <input type="file" class="form-control mb-2" id="importFile" accept=".json">
                                <button class="btn btn-outline-success" onclick="importConfig()">
                                    <i class="fas fa-file-import me-2"></i>导入配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>重置系统
                    </h6>
                    <p class="mb-2">这将删除所有实例配置和自定义设置，恢复到初始状态。</p>
                    <button class="btn btn-outline-danger" onclick="resetSystem()">
                        <i class="fas fa-trash-restore me-2"></i>重置系统
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传图标模态框 -->
<div class="modal fade" id="uploadIconModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius: 12px; border: none;">
            <div class="modal-header" style="background: var(--success-color); color: white; border-radius: 12px 12px 0 0;">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>上传图标
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadIconForm">
                    <div class="mb-3">
                        <label for="iconFile" class="form-label">选择图标文件</label>
                        <input type="file" class="form-control" id="iconFile" accept=".ico,.png,.jpg,.jpeg,.gif,.bmp">
                        <div class="form-text">支持 ICO、PNG、JPG、GIF、BMP 格式，最大5MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="iconId" class="form-label">图标ID</label>
                        <input type="text" class="form-control" id="iconId" placeholder="例如：my_custom_icon">
                        <div class="form-text">用于标识图标的唯一ID</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="iconName" class="form-label">图标名称</label>
                        <input type="text" class="form-control" id="iconName" placeholder="例如：我的自定义图标">
                    </div>
                    
                    <div class="mb-3">
                        <label for="iconDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="iconDescription" rows="2" placeholder="简要描述这个图标的用途"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="iconCategory" class="form-label">分类</label>
                        <select class="form-control" id="iconCategory">
                            <option value="custom">自定义</option>
                            <option value="business">商务</option>
                            <option value="personal">个人</option>
                            <option value="development">开发</option>
                            <option value="entertainment">娱乐</option>
                            <option value="shopping">购物</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="uploadIcon()">
                    <i class="fas fa-upload me-2"></i>上传图标
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadGeneralSettings();
    loadChromeSettings();
    loadIconsList();
});

// 加载常规设置
function loadGeneralSettings() {
    // 这里可以从API加载设置
    console.log('Loading general settings...');
}

// 保存常规设置
function saveGeneralSettings() {
    showToast('常规设置保存成功！', 'success');
}

// 加载Chrome设置
function loadChromeSettings() {
    console.log('Loading Chrome settings...');
}

// 保存Chrome设置
function saveChromeSettings() {
    showToast('Chrome设置保存成功！', 'success');
}

// 加载图标列表
async function loadIconsList() {
    try {
        const result = await apiRequest('/api/icons');
        const icons = result.data;
        
        const iconsContainer = document.getElementById('iconsList');
        if (icons.length === 0) {
            iconsContainer.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无自定义图标</h5>
                    <p class="text-muted">点击"上传图标"添加您的第一个自定义图标</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="row">';
        icons.forEach(icon => {
            html += `
                <div class="col-md-3 mb-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-image fa-2x mb-2 text-primary"></i>
                            <h6 class="card-title">${icon.name}</h6>
                            <p class="card-text small text-muted">${icon.description || '无描述'}</p>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-danger" onclick="deleteIcon('${icon.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        iconsContainer.innerHTML = html;
        
    } catch (error) {
        document.getElementById('iconsList').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载图标列表失败: ${error.message}
            </div>
        `;
    }
}

// 显示上传图标模态框
function showUploadIconModal() {
    const modal = new bootstrap.Modal(document.getElementById('uploadIconModal'));
    modal.show();
}

// 上传图标
function uploadIcon() {
    showToast('图标上传功能开发中...', 'info');
}

// 删除图标
async function deleteIcon(iconId) {
    if (!confirm(`确定要删除图标 "${iconId}" 吗？`)) {
        return;
    }
    
    try {
        await apiRequest(`/api/icons/${iconId}`, { method: 'DELETE' });
        showToast('图标删除成功！', 'success');
        loadIconsList();
    } catch (error) {
        showToast(`删除图标失败: ${error.message}`, 'error');
    }
}

// 导出配置
function exportConfig() {
    showToast('配置导出功能开发中...', 'info');
}

// 导入配置
function importConfig() {
    showToast('配置导入功能开发中...', 'info');
}

// 重置系统
function resetSystem() {
    if (!confirm('确定要重置系统吗？这将删除所有数据！')) {
        return;
    }
    showToast('系统重置功能开发中...', 'warning');
}
</script>
{% endblock %}

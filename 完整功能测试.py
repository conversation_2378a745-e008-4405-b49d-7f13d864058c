#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v0.002 - 完整功能测试
测试所有主要功能的集成测试脚本

Author: AI Architecture System
Date: 2025-01-29
"""

import requests
import json
import time
import sys

API_BASE = 'http://127.0.0.1:5000'

def test_api(endpoint, method='GET', data=None, description="", expect_json=True):
    """测试API接口"""
    try:
        if method == 'GET':
            response = requests.get(f'{API_BASE}{endpoint}')
        elif method == 'POST':
            response = requests.post(f'{API_BASE}{endpoint}', json=data)
        elif method == 'PUT':
            response = requests.put(f'{API_BASE}{endpoint}', json=data)
        elif method == 'DELETE':
            response = requests.delete(f'{API_BASE}{endpoint}')

        if expect_json:
            result = response.json()
            status = "✅ 成功" if result.get('success', response.status_code == 200) else "❌ 失败"
            print(f"{status} {description}: {endpoint}")

            if not result.get('success', response.status_code == 200):
                print(f"   错误: {result.get('error', '未知错误')}")

            return result
        else:
            # 对于HTML页面，只检查状态码
            status = "✅ 成功" if response.status_code == 200 else "❌ 失败"
            print(f"{status} {description}: {endpoint} (状态码: {response.status_code})")
            return {'success': response.status_code == 200, 'status_code': response.status_code}

    except Exception as e:
        print(f"❌ 失败 {description}: {endpoint} - 异常: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主测试函数"""
    print("🚀 浏览器多账号绿色版 v0.002 - 完整功能测试")
    print("=" * 60)
    
    # 1. 基础连接测试
    print("\n📡 1. 基础连接测试")
    health = test_api('/health', description="健康检查")
    test_api('/', description="主页访问", expect_json=False)
    
    # 2. 实例管理测试
    print("\n📋 2. 实例管理测试")
    instances = test_api('/api/instances', description="获取实例列表")
    
    # 创建测试实例
    test_instance_data = {
        'name': 'integration_test',
        'display_name': 'Chrome - 集成测试',
        'description': '用于集成测试的实例',
        'homepage': 'https://www.google.com',
        'group': 'test'
    }
    
    create_result = test_api('/api/instances', 'POST', test_instance_data, "创建测试实例")
    
    if create_result.get('success'):
        # 获取单个实例
        test_api('/api/instances/integration_test', description="获取单个实例")
        
        # 更新实例
        update_data = {
            'display_name': 'Chrome - 集成测试(更新)',
            'description': '更新后的描述',
            'group': 'updated'
        }
        test_api('/api/instances/integration_test', 'PUT', update_data, "更新实例")
        
        # 启动实例
        test_api('/api/instances/integration_test/launch', 'POST', description="启动实例")
        
        # 获取实例状态
        test_api('/api/instances/integration_test/status', description="获取实例状态")
    
    # 3. 批量操作测试
    print("\n🔄 3. 批量操作测试")
    test_api('/api/batch/templates', description="获取批量模板")
    
    # 批量启动测试
    batch_launch_data = {'instances': ['test', 'test_work']}
    test_api('/api/batch/launch', 'POST', batch_launch_data, "批量启动实例")
    
    # 4. 分组管理测试
    print("\n📁 4. 分组管理测试")
    test_api('/api/groups', description="获取所有分组")
    test_api('/api/groups/work', description="获取工作分组实例")
    
    # 5. 导入导出测试
    print("\n💾 5. 导入导出测试")
    export_result = test_api('/api/export', description="导出配置")
    
    # 6. 统计信息测试
    print("\n📊 6. 统计信息测试")
    test_api('/api/statistics', description="获取统计信息")
    test_api('/api/instances/status/all', description="获取所有实例状态")
    
    # 7. 清理测试数据
    print("\n🧹 7. 清理测试数据")
    if create_result.get('success'):
        test_api('/api/instances/integration_test', 'DELETE', description="删除测试实例")
    
    # 8. 最终状态检查
    print("\n🏁 8. 最终状态检查")
    final_instances = test_api('/api/instances', description="最终实例列表")
    final_health = test_api('/health', description="最终健康检查")
    
    # 测试总结
    print("\n" + "=" * 60)
    print("📋 测试总结")
    
    if final_health.get('chrome_portable'):
        print("✅ Chrome Portable: 可用")
    else:
        print("❌ Chrome Portable: 不可用")
    
    if final_instances.get('success'):
        instance_count = len(final_instances.get('data', []))
        print(f"✅ 实例管理: 正常 ({instance_count}个实例)")
    else:
        print("❌ 实例管理: 异常")
    
    if final_health.get('status') == 'healthy':
        print("✅ 系统状态: 健康")
    else:
        print("❌ 系统状态: 异常")
    
    print("\n🎉 集成测试完成！")
    
    # 显示当前实例状态
    if final_instances.get('success'):
        print("\n📋 当前实例列表:")
        for inst in final_instances['data']:
            status_icon = "🟢" if inst.get('status') == 'running' else "🔴"
            print(f"   {status_icon} {inst['name']}: {inst['display_name']} ({inst.get('group', 'default')})")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)

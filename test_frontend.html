<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>浏览器多账号管理系统 - 前端功能测试</h1>
    
    <div class="test-section">
        <h2>API连接测试</h2>
        <button onclick="testHealth()">测试健康检查</button>
        <button onclick="testInstances()">测试获取实例</button>
        <button onclick="testTemplates()">测试批量模板</button>
        <div id="apiResults"></div>
    </div>
    
    <div class="test-section">
        <h2>实例操作测试</h2>
        <button onclick="createTestInstance()">创建测试实例</button>
        <button onclick="launchTestInstance()">启动测试实例</button>
        <button onclick="deleteTestInstance()">删除测试实例</button>
        <div id="instanceResults"></div>
    </div>
    
    <div class="test-section">
        <h2>批量操作测试</h2>
        <button onclick="testBatchLaunch()">批量启动测试</button>
        <button onclick="testExport()">测试导出</button>
        <button onclick="testStatistics()">测试统计信息</button>
        <div id="batchResults"></div>
    </div>

    <div class="test-section">
        <h2>前端功能测试</h2>
        <button onclick="testJavaScriptFunctions()">测试JavaScript函数</button>
        <button onclick="testLocalStorage()">测试本地存储</button>
        <button onclick="testEventHandlers()">测试事件处理</button>
        <div id="frontendResults"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:5000';
        
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return data;
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML += `<div class="${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }
        
        async function testHealth() {
            const result = await apiRequest('/health');
            showResult('apiResults', `健康检查: ${result.status || '失败'} - Chrome: ${result.chrome_portable ? '可用' : '不可用'}`);
        }
        
        async function testInstances() {
            const result = await apiRequest('/api/instances');
            showResult('apiResults', `获取实例: ${result.success ? '成功' : '失败'} - 实例数: ${result.data ? result.data.length : 0}`);
        }
        
        async function testTemplates() {
            const result = await apiRequest('/api/batch/templates');
            showResult('apiResults', `批量模板: ${result.success ? '成功' : '失败'} - 模板数: ${result.data ? Object.keys(result.data).length : 0}`);
        }
        
        async function createTestInstance() {
            const data = {
                name: 'frontend_test',
                display_name: '前端测试实例',
                description: '用于前端功能测试',
                homepage: 'https://www.google.com',
                group: 'test'
            };
            const result = await apiRequest('/api/instances', {
                method: 'POST',
                body: JSON.stringify(data)
            });
            showResult('instanceResults', `创建实例: ${result.success ? '成功' : '失败'} - ${result.error || ''}`, result.success);
        }
        
        async function launchTestInstance() {
            const result = await apiRequest('/api/instances/frontend_test/launch', {
                method: 'POST'
            });
            showResult('instanceResults', `启动实例: ${result.success ? '成功' : '失败'} - ${result.message || result.error || ''}`, result.success);
        }
        
        async function deleteTestInstance() {
            const result = await apiRequest('/api/instances/frontend_test', {
                method: 'DELETE'
            });
            showResult('instanceResults', `删除实例: ${result.success ? '成功' : '失败'} - ${result.message || result.error || ''}`, result.success);
        }
        
        async function testBatchLaunch() {
            const result = await apiRequest('/api/batch/launch', {
                method: 'POST',
                body: JSON.stringify({ instances: ['test', 'test_work'] })
            });
            showResult('batchResults', `批量启动: ${result.success ? '成功' : '失败'} - ${result.message || result.error || ''}`, result.success);
        }
        
        async function testExport() {
            const result = await apiRequest('/api/export');
            showResult('batchResults', `导出配置: ${result.success ? '成功' : '失败'} - 实例数: ${result.data ? result.data.instances.length : 0}`, result.success);
        }

        async function testStatistics() {
            const result = await apiRequest('/api/statistics');
            showResult('batchResults', `统计信息: ${result.success ? '成功' : '失败'} - 总实例: ${result.data ? result.data.total_instances : 0}`, result.success);
        }

        function testJavaScriptFunctions() {
            try {
                // 测试基本JavaScript功能
                const testArray = [1, 2, 3];
                const testObject = { name: 'test', value: 123 };
                const testString = JSON.stringify(testObject);
                const testParsed = JSON.parse(testString);

                showResult('frontendResults', `JavaScript基础功能: 成功 - 数组长度: ${testArray.length}, 对象解析: ${testParsed.name}`, true);

                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.innerHTML = '<span>测试内容</span>';
                const spanContent = testDiv.querySelector('span').textContent;

                showResult('frontendResults', `DOM操作: 成功 - 内容: ${spanContent}`, true);

            } catch (error) {
                showResult('frontendResults', `JavaScript测试失败: ${error.message}`, false);
            }
        }

        function testLocalStorage() {
            try {
                // 测试本地存储
                const testKey = 'browser_test_key';
                const testValue = 'browser_test_value';

                localStorage.setItem(testKey, testValue);
                const retrievedValue = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);

                showResult('frontendResults', `本地存储: ${retrievedValue === testValue ? '成功' : '失败'} - 值: ${retrievedValue}`, retrievedValue === testValue);

            } catch (error) {
                showResult('frontendResults', `本地存储测试失败: ${error.message}`, false);
            }
        }

        function testEventHandlers() {
            try {
                // 测试事件处理
                const testButton = document.createElement('button');
                testButton.textContent = '测试按钮';

                let eventTriggered = false;
                testButton.addEventListener('click', function() {
                    eventTriggered = true;
                });

                // 模拟点击
                testButton.click();

                showResult('frontendResults', `事件处理: ${eventTriggered ? '成功' : '失败'} - 事件触发: ${eventTriggered}`, eventTriggered);

            } catch (error) {
                showResult('frontendResults', `事件处理测试失败: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
